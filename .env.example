# ===========================================
# kodeXGuard Environment Configuration
# ===========================================

# Application Settings
NODE_ENV=development
APP_NAME=kodeXGuard
APP_VERSION=1.0.0
APP_URL=http://localhost:3000
API_URL=http://localhost:8000

# Database Configuration
# MySQL (Production/Web)
DB_HOST=localhost
DB_PORT=3306
DB_NAME=kodexguard
DB_USER=root
DB_PASSWORD=your_password

# SQLite (Mobile/Local)
SQLITE_DB_PATH=./data/kodexguard.db

# JWT Authentication
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=7d
REFRESH_TOKEN_SECRET=your-refresh-token-secret
REFRESH_TOKEN_EXPIRES_IN=30d

# API Keys & External Services
OPENAI_API_KEY=your-openai-api-key
VIRUSTOTAL_API_KEY=your-virustotal-api-key
SHODAN_API_KEY=your-shodan-api-key

# WhatsApp Bot Configuration
WHATSAPP_SESSION_PATH=./sessions/whatsapp
WHATSAPP_WEBHOOK_URL=http://localhost:8000/webhook/whatsapp

# Telegram Bot Configuration
TELEGRAM_BOT_TOKEN=your-telegram-bot-token
TELEGRAM_WEBHOOK_URL=http://localhost:8000/webhook/telegram

# Payment Gateway
# Xendit
XENDIT_SECRET_KEY=your-xendit-secret-key
XENDIT_WEBHOOK_TOKEN=your-xendit-webhook-token

# Midtrans
MIDTRANS_SERVER_KEY=your-midtrans-server-key
MIDTRANS_CLIENT_KEY=your-midtrans-client-key
MIDTRANS_IS_PRODUCTION=false

# File Upload & Storage
MAX_FILE_SIZE=50MB
UPLOAD_PATH=./uploads
QUARANTINE_PATH=./quarantine
ALLOWED_FILE_TYPES=.php,.js,.zip,.apk,.exe,.pdf,.txt

# Security Scanner Configuration
SCAN_TIMEOUT=300000
MAX_CONCURRENT_SCANS=5
NMAP_PATH=/usr/bin/nmap
SQLMAP_PATH=/usr/bin/sqlmap

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Redis (for caching and queues)
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL=info
LOG_FILE_PATH=./logs/app.log

# Development Settings
DEBUG=true
ENABLE_CORS=true
CORS_ORIGIN=http://localhost:3000

# Production Settings (uncomment for production)
# NODE_ENV=production
# DEBUG=false
# ENABLE_CORS=false
# SSL_CERT_PATH=/path/to/cert.pem
# SSL_KEY_PATH=/path/to/key.pem

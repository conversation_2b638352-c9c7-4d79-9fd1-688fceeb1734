import React from 'react';
import {
  TouchableOpacity,
  Text,
  StyleSheet,
  ActivityIndicator,
  ViewStyle,
  TextStyle,
  TouchableOpacityProps,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';

import { COLORS, FONTS, SPACING, BORDER_RADIUS, SHADOWS } from '@/constants/theme';

interface ButtonProps extends TouchableOpacityProps {
  title: string;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger';
  size?: 'sm' | 'md' | 'lg';
  loading?: boolean;
  disabled?: boolean;
  fullWidth?: boolean;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  gradient?: boolean;
  style?: ViewStyle;
  textStyle?: TextStyle;
}

export const Button: React.FC<ButtonProps> = ({
  title,
  variant = 'primary',
  size = 'md',
  loading = false,
  disabled = false,
  fullWidth = false,
  leftIcon,
  rightIcon,
  gradient = false,
  style,
  textStyle,
  onPress,
  ...props
}) => {
  const isDisabled = disabled || loading;

  const buttonStyles = [
    styles.button,
    styles[`button_${variant}`],
    styles[`button_${size}`],
    fullWidth && styles.fullWidth,
    isDisabled && styles.disabled,
    style,
  ];

  const textStyles = [
    styles.text,
    styles[`text_${variant}`],
    styles[`text_${size}`],
    isDisabled && styles.textDisabled,
    textStyle,
  ];

  const handlePress = () => {
    if (!isDisabled && onPress) {
      onPress();
    }
  };

  const renderContent = () => (
    <>
      {loading && (
        <ActivityIndicator
          size="small"
          color={variant === 'primary' ? COLORS.white : COLORS.primary[600]}
          style={styles.loader}
        />
      )}
      {leftIcon && !loading && leftIcon}
      <Text style={textStyles}>{title}</Text>
      {rightIcon && !loading && rightIcon}
    </>
  );

  if (gradient && variant === 'primary' && !isDisabled) {
    return (
      <TouchableOpacity
        style={[styles.button, styles[`button_${size}`], fullWidth && styles.fullWidth]}
        onPress={handlePress}
        activeOpacity={0.8}
        {...props}
      >
        <LinearGradient
          colors={[COLORS.primary[500], COLORS.primary[700]]}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
          style={[styles.gradient, styles[`button_${size}`]]}
        >
          {renderContent()}
        </LinearGradient>
      </TouchableOpacity>
    );
  }

  return (
    <TouchableOpacity
      style={buttonStyles}
      onPress={handlePress}
      activeOpacity={isDisabled ? 1 : 0.8}
      {...props}
    >
      {renderContent()}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: BORDER_RADIUS.md,
    paddingHorizontal: SPACING.md,
    ...SHADOWS.sm,
  },
  
  // Variants
  button_primary: {
    backgroundColor: COLORS.primary[600],
  },
  button_secondary: {
    backgroundColor: COLORS.secondary[100],
    borderWidth: 1,
    borderColor: COLORS.secondary[300],
  },
  button_outline: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: COLORS.primary[600],
  },
  button_ghost: {
    backgroundColor: 'transparent',
  },
  button_danger: {
    backgroundColor: COLORS.danger[600],
  },

  // Sizes
  button_sm: {
    height: 36,
    paddingHorizontal: SPACING.sm,
  },
  button_md: {
    height: 44,
    paddingHorizontal: SPACING.md,
  },
  button_lg: {
    height: 52,
    paddingHorizontal: SPACING.lg,
  },

  // States
  disabled: {
    opacity: 0.5,
  },
  fullWidth: {
    width: '100%',
  },

  // Text styles
  text: {
    fontFamily: FONTS.medium,
    textAlign: 'center',
    fontWeight: '600',
  },
  
  // Text variants
  text_primary: {
    color: COLORS.white,
  },
  text_secondary: {
    color: COLORS.secondary[700],
  },
  text_outline: {
    color: COLORS.primary[600],
  },
  text_ghost: {
    color: COLORS.primary[600],
  },
  text_danger: {
    color: COLORS.white,
  },

  // Text sizes
  text_sm: {
    fontSize: FONTS.sizes.sm,
    lineHeight: FONTS.lineHeights.sm,
  },
  text_md: {
    fontSize: FONTS.sizes.base,
    lineHeight: FONTS.lineHeights.base,
  },
  text_lg: {
    fontSize: FONTS.sizes.lg,
    lineHeight: FONTS.lineHeights.lg,
  },

  textDisabled: {
    opacity: 0.7,
  },

  // Gradient
  gradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: BORDER_RADIUS.md,
    paddingHorizontal: SPACING.md,
  },

  // Loader
  loader: {
    marginRight: SPACING.xs,
  },
});

export default Button;

import { Metada<PERSON> } from 'next';
import { <PERSON> } from '@/components/landing/hero';
import { Features } from '@/components/landing/features';
import { Stats } from '@/components/landing/stats';
import { Pricing } from '@/components/landing/pricing';
import { CTA } from '@/components/landing/cta';
import { Header } from '@/components/layout/header';
import { Footer } from '@/components/layout/footer';

export const metadata: Metadata = {
  title: 'kodeXGuard - Platform Keamanan Siber Profesional',
  description: 'Platform keamanan siber dan bug hunting untuk mendeteksi kerentanan, memindai file berbahaya, dan menjalankan penetration testing secara real-time.',
};

export default function HomePage() {
  return (
    <div className="min-h-screen">
      <Header />
      <main>
        <Hero />
        <Features />
        <Stats />
        <Pricing />
        <CTA />
      </main>
      <Footer />
    </div>
  );
}

// Export API client
export { apiClient } from './client';

// Export authentication API
export { authApi } from './auth';
export type { LoginRequest, RegisterRequest, AuthResponse, RefreshTokenResponse } from './auth';

// Export scans API
export { scanApi, ScanWebSocket } from './scans';
export type { 
  ScanRequest, 
  Vulnerability, 
  Scan, 
  ScanStats 
} from './scans';

// Export files API
export { fileApi, FileAnalysisWebSocket } from './files';
export type { 
  FileAnalysisRequest, 
  FileAnalysisResult, 
  FileStats 
} from './files';

// Export reports API
export { reportApi, ReportWebSocket } from './reports';
export type { 
  ReportRequest, 
  Report, 
  ReportStats 
} from './reports';

// Export AI API
export { aiApi, AIChatWebSocket } from './ai';
export type { 
  AIAnalysisRequest, 
  AIAnalysisResponse, 
  AIProviderStatus, 
  AIUsageStats 
} from './ai';

// Export dashboard API
export { dashboardApi, DashboardWebSocket } from './dashboard';
export type { 
  DashboardStats, 
  SystemHealth, 
  UserActivity 
} from './dashboard';

// Export API keys API
export { apiKeyApi, apiKeyUtils } from './apiKeys';
export type {
  ApiKeyRequest,
  ApiKey,
  ApiKeyUsage,
  ApiKeyStats
} from './apiKeys';

// Export users API
export { userApi, adminUserApi } from './users';
export type {
  UserProfile,
  UpdateProfileRequest,
  SecuritySettings,
  NotificationSettings,
  UserStats
} from './users';

// Export subscriptions API
export { subscriptionApi, adminSubscriptionApi } from './subscriptions';
export type {
  SubscriptionPlan,
  Subscription,
  PaymentMethod,
  Invoice,
  UsageStats
} from './subscriptions';

// Utility functions for common API operations
export const apiUtils = {
  // Format error messages from API responses
  formatErrorMessage(error: any): string {
    if (error.response?.data?.message) {
      return error.response.data.message;
    }
    if (error.response?.data?.error) {
      return error.response.data.error;
    }
    if (error.message) {
      return error.message;
    }
    return 'An unexpected error occurred';
  },

  // Check if error is a network error
  isNetworkError(error: any): boolean {
    return !error.response && error.request;
  },

  // Check if error is a server error (5xx)
  isServerError(error: any): boolean {
    return error.response?.status >= 500;
  },

  // Check if error is a client error (4xx)
  isClientError(error: any): boolean {
    return error.response?.status >= 400 && error.response?.status < 500;
  },

  // Check if error is unauthorized (401)
  isUnauthorizedError(error: any): boolean {
    return error.response?.status === 401;
  },

  // Check if error is forbidden (403)
  isForbiddenError(error: any): boolean {
    return error.response?.status === 403;
  },

  // Check if error is not found (404)
  isNotFoundError(error: any): boolean {
    return error.response?.status === 404;
  },

  // Check if error is rate limited (429)
  isRateLimitError(error: any): boolean {
    return error.response?.status === 429;
  },

  // Extract validation errors from API response
  extractValidationErrors(error: any): Record<string, string[]> {
    if (error.response?.data?.errors) {
      return error.response.data.errors;
    }
    return {};
  },

  // Format file size for display
  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  },

  // Format date for display
  formatDate(dateString: string): string {
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
  },

  // Format relative time (e.g., "2 hours ago")
  formatRelativeTime(dateString: string): string {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) {
      return 'Just now';
    }

    const diffInMinutes = Math.floor(diffInSeconds / 60);
    if (diffInMinutes < 60) {
      return `${diffInMinutes} minute${diffInMinutes > 1 ? 's' : ''} ago`;
    }

    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) {
      return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`;
    }

    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 30) {
      return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`;
    }

    const diffInMonths = Math.floor(diffInDays / 30);
    if (diffInMonths < 12) {
      return `${diffInMonths} month${diffInMonths > 1 ? 's' : ''} ago`;
    }

    const diffInYears = Math.floor(diffInMonths / 12);
    return `${diffInYears} year${diffInYears > 1 ? 's' : ''} ago`;
  },

  // Get severity color for vulnerabilities
  getSeverityColor(severity: string): string {
    switch (severity.toLowerCase()) {
      case 'critical':
        return 'red';
      case 'high':
        return 'orange';
      case 'medium':
        return 'yellow';
      case 'low':
        return 'blue';
      case 'info':
        return 'gray';
      default:
        return 'gray';
    }
  },

  // Get status color for scans/analyses
  getStatusColor(status: string): string {
    switch (status.toLowerCase()) {
      case 'completed':
        return 'green';
      case 'running':
      case 'analyzing':
      case 'generating':
        return 'blue';
      case 'pending':
        return 'yellow';
      case 'failed':
        return 'red';
      case 'cancelled':
        return 'gray';
      default:
        return 'gray';
    }
  },

  // Validate email format
  isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  },

  // Validate URL format
  isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  },

  // Generate random ID
  generateId(): string {
    return Math.random().toString(36).substring(2, 11);
  },

  // Debounce function for search inputs
  debounce<T extends (...args: any[]) => any>(
    func: T,
    wait: number
  ): (...args: Parameters<T>) => void {
    let timeout: NodeJS.Timeout;
    return (...args: Parameters<T>) => {
      clearTimeout(timeout);
      timeout = setTimeout(() => func(...args), wait);
    };
  },

  // Throttle function for API calls
  throttle<T extends (...args: any[]) => any>(
    func: T,
    limit: number
  ): (...args: Parameters<T>) => void {
    let inThrottle: boolean;
    return (...args: Parameters<T>) => {
      if (!inThrottle) {
        func(...args);
        inThrottle = true;
        setTimeout(() => (inThrottle = false), limit);
      }
    };
  }
};

{"name": "kodexguard", "version": "1.0.0", "description": "kodeXGuard - Platform Keamanan Siber dan Bug Hunting Profesional", "private": true, "packageManager": "bun@1.0.0", "workspaces": ["apps/*", "packages/*"], "scripts": {"dev": "turbo run dev", "build": "turbo run build", "test": "turbo run test", "lint": "turbo run lint", "clean": "turbo run clean", "type-check": "turbo run type-check", "dev:web": "cd apps/web && npm run dev", "dev:mobile": "cd apps/mobile && npm run start", "dev:api": "cd packages/api && npm run dev", "dev:bot": "cd packages/bot && npm run dev", "install:all": "npm install && npm run install:workspaces", "install:workspaces": "npm install --workspaces", "setup": "npm run install:all && npm run build:shared", "build:shared": "cd packages/shared && npm run build", "scanner:test": "cd packages/scanner && npm run test", "file-analyzer:test": "cd packages/file-analyzer && npm run test", "generate:report": "cd packages/pdf-report && npm run generate"}, "devDependencies": {"@types/node": "^20.10.0", "@typescript-eslint/eslint-plugin": "^6.13.0", "@typescript-eslint/parser": "^6.13.0", "eslint": "^8.54.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.1", "prettier": "^3.1.0", "turbo": "^1.11.0", "typescript": "^5.3.0", "concurrently": "^8.2.2", "cross-env": "^7.0.3"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-username/kodexguard.git"}, "keywords": ["cybersecurity", "bug-hunting", "vulnerability-scanner", "penetration-testing", "security-tools", "malware-detection", "web-security", "mobile-security"], "author": "kodeXGuard Team", "license": "MIT", "bugs": {"url": "https://github.com/your-username/kodexguard/issues"}, "homepage": "https://github.com/your-username/kodexguard#readme"}
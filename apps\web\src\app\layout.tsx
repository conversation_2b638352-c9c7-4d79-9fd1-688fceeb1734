import type { Metada<PERSON> } from 'next';
import { Inter, JetBrains_Mono } from 'next/font/google';
import { ThemeProvider } from 'next-themes';
import { Toaster } from 'react-hot-toast';

import { QueryProvider } from '@/lib/providers/query-provider';
import { AuthProvider } from '@/lib/providers/auth-provider';
import '@/styles/globals.css';

const inter = Inter({
  subsets: ['latin'],
  variable: '--font-inter',
  display: 'swap',
});

const jetbrainsMono = JetBrains_Mono({
  subsets: ['latin'],
  variable: '--font-jetbrains-mono',
  display: 'swap',
});

export const metadata: Metadata = {
  title: {
    default: 'kodeXGuard - Platform Keamanan Siber Profesional',
    template: '%s | kodeXGuard',
  },
  description: 'Platform keamanan siber dan bug hunting untuk mendeteksi kerentanan, memindai file berbahaya, dan menjalankan penetration testing secara real-time.',
  keywords: [
    'cybersecurity',
    'bug hunting',
    'vulnerability scanner',
    'penetration testing',
    'security tools',
    'malware detection',
    'web security',
    'kodeXGuard',
  ],
  authors: [{ name: 'kodeXGuard Team' }],
  creator: 'kodeXGuard Team',
  publisher: 'kodeXGuard',
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  openGraph: {
    type: 'website',
    locale: 'id_ID',
    url: 'https://kodexguard.com',
    siteName: 'kodeXGuard',
    title: 'kodeXGuard - Platform Keamanan Siber Profesional',
    description: 'Platform keamanan siber dan bug hunting untuk mendeteksi kerentanan, memindai file berbahaya, dan menjalankan penetration testing secara real-time.',
    images: [
      {
        url: '/og-image.png',
        width: 1200,
        height: 630,
        alt: 'kodeXGuard - Platform Keamanan Siber',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'kodeXGuard - Platform Keamanan Siber Profesional',
    description: 'Platform keamanan siber dan bug hunting untuk mendeteksi kerentanan, memindai file berbahaya, dan menjalankan penetration testing secara real-time.',
    images: ['/og-image.png'],
    creator: '@kodexguard',
  },
  icons: {
    icon: '/favicon.ico',
    shortcut: '/favicon-16x16.png',
    apple: '/apple-touch-icon.png',
  },
  manifest: '/site.webmanifest',
  verification: {
    google: 'your-google-verification-code',
    yandex: 'your-yandex-verification-code',
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="id" suppressHydrationWarning>
      <body className={`${inter.variable} ${jetbrainsMono.variable} font-sans`}>
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          <QueryProvider>
            <AuthProvider>
              <div className="min-h-screen bg-white dark:bg-secondary-900">
                {children}
              </div>
              <Toaster
                position="top-right"
                toastOptions={{
                  duration: 4000,
                  style: {
                    background: 'var(--toast-bg)',
                    color: 'var(--toast-color)',
                    border: '1px solid var(--toast-border)',
                  },
                  success: {
                    iconTheme: {
                      primary: '#22c55e',
                      secondary: '#ffffff',
                    },
                  },
                  error: {
                    iconTheme: {
                      primary: '#ef4444',
                      secondary: '#ffffff',
                    },
                  },
                }}
              />
            </AuthProvider>
          </QueryProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}

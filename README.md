# 🛡️ kodeXGuard

**Platform Keamanan Siber dan Bug Hunting Profesional**

kodeXGuard adalah platform komprehensif untuk deteksi kerentanan, analisis file berbahaya, dan penetration testing real-time dengan dukungan mobile native dan integrasi BOT WhatsApp/Telegram.

## 🎯 Fitur Utama

### 🔍 Security Scanner
- **Vulnerability Detection**: SQL Injection, XSS, CSRF, dan 50+ jenis kerentanan lainnya
- **Network Scanning**: Port scanning, subdomain discovery, SSL analysis
- **Header Analysis**: Security headers, misconfigurations, dan best practices
- **Real-time Monitoring**: Live scanning dengan notifikasi instant

### 📁 File Analyzer
- **Malware Detection**: Backdoor, trojan, RAT, dan script berbahaya
- **Multi-format Support**: PHP, JavaScript, ZIP, APK, EXE, dan lainnya
- **AI-powered Classification**: Machine learning untuk deteksi ancaman baru
- **Quarantine System**: Isolasi otomatis file berbahaya

### 📊 Reporting & Dashboard
- **PDF Reports**: Laporan profesional dengan severity dan rekomendasi
- **Real-time Dashboard**: Statistik keamanan dan trend analysis
- **Custom Templates**: Template laporan yang dapat disesuaikan
- **Digital Signature**: Tanda tangan digital untuk autentikasi laporan

### 🤖 Bot Integration
- **WhatsApp Bot**: Scanning dan monitoring via WhatsApp
- **Telegram Bot**: Command-based operations dan notifikasi
- **Remote Operations**: Kontrol penuh platform melalui chat

### 💳 Subscription System
- **Tiered Plans**: Free, Pro, Enterprise dengan fitur berbeda
- **Payment Gateway**: Xendit, Midtrans, manual bank transfer
- **Feature Limiting**: Kontrol akses berdasarkan subscription

## 🏗️ Arsitektur

```
kodeXGuard/
├── apps/
│   ├── web/                 # Next.js Web Frontend
│   └── mobile/              # React Native Mobile App
├── packages/
│   ├── api/                 # Backend REST API
│   ├── bot/                 # WhatsApp & Telegram Bots
│   ├── scanner/             # Security Scanner Engine
│   ├── file-analyzer/       # File Analysis Module
│   ├── pdf-report/          # PDF Report Generator
│   ├── ai-assistant/        # AI Assistant Module
│   └── shared/              # Shared Utilities & Types
└── scripts/                 # External Tools & Scripts
```

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- npm 9+
- MySQL 8.0+
- Redis (optional, untuk caching)

### Installation

1. **Clone repository**
```bash
git clone https://github.com/your-username/kodexguard.git
cd kodexguard
```

2. **Install dependencies**
```bash
npm run setup
```

3. **Environment setup**
```bash
cp .env.example .env
# Edit .env dengan konfigurasi Anda
```

4. **Database setup**
```bash
# Buat database MySQL
mysql -u root -p -e "CREATE DATABASE kodexguard;"

# Run migrations
npm run db:migrate
```

5. **Start development**
```bash
# Start all services
npm run dev

# Atau start individual services
npm run dev:web      # Web frontend (port 3000)
npm run dev:api      # Backend API (port 8000)
npm run dev:mobile   # Mobile app
npm run dev:bot      # Bot services
```

## 📱 Platform Support

### Web Application
- **Framework**: Next.js 14 dengan App Router
- **Styling**: TailwindCSS dengan custom components
- **State Management**: Zustand + React Query
- **Authentication**: JWT dengan refresh tokens

### Mobile Application
- **Framework**: React Native dengan Expo
- **Navigation**: React Navigation 6
- **Database**: SQLite untuk offline support
- **Push Notifications**: Expo Notifications

### Backend API
- **Runtime**: Node.js dengan Express/Fastify
- **Database**: MySQL (production), SQLite (mobile)
- **Authentication**: JWT + Role-based access control
- **File Upload**: Multer dengan virus scanning

## 🔧 Development

### Available Scripts

```bash
# Development
npm run dev                  # Start all services
npm run dev:web             # Web frontend only
npm run dev:mobile          # Mobile app only
npm run dev:api             # Backend API only

# Building
npm run build               # Build all packages
npm run build:web           # Build web app
npm run build:mobile        # Build mobile app

# Testing
npm run test                # Run all tests
npm run test:scanner        # Test scanner engine
npm run test:file-analyzer  # Test file analyzer

# Utilities
npm run lint                # Lint all code
npm run type-check          # TypeScript checking
npm run clean               # Clean build artifacts
```

### Code Structure

- **Monorepo**: Menggunakan npm workspaces + Turbo
- **TypeScript**: Strict mode dengan path mapping
- **ESLint + Prettier**: Code formatting dan linting
- **Husky**: Pre-commit hooks untuk quality assurance

## 🔒 Security Features

### Vulnerability Detection
- SQL Injection (Error-based, Blind, Time-based)
- Cross-Site Scripting (Reflected, Stored, DOM)
- Cross-Site Request Forgery (CSRF)
- Local/Remote File Inclusion (LFI/RFI)
- Command Injection
- XML External Entity (XXE)
- Server-Side Request Forgery (SSRF)
- Directory Traversal
- Authentication Bypass
- Session Management Issues

### File Analysis
- **Static Analysis**: Signature-based detection
- **Dynamic Analysis**: Behavior monitoring
- **Heuristic Detection**: Pattern recognition
- **AI Classification**: Machine learning models

## 📖 API Documentation

API documentation tersedia di `/docs` setelah menjalankan server development.

### Authentication
```bash
POST /api/auth/login
POST /api/auth/register
POST /api/auth/refresh
```

### Scanning
```bash
POST /api/scan/url          # Scan website
POST /api/scan/file         # Analyze file
GET  /api/scan/:id          # Get scan results
```

### Reports
```bash
GET  /api/reports           # List reports
POST /api/reports/generate  # Generate PDF
GET  /api/reports/:id/pdf   # Download PDF
```

## 🤝 Contributing

1. Fork repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Open Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: [docs.kodexguard.com](https://docs.kodexguard.com)
- **Issues**: [GitHub Issues](https://github.com/your-username/kodexguard/issues)
- **Discord**: [Join our community](https://discord.gg/kodexguard)
- **Email**: <EMAIL>

## 🙏 Acknowledgments

- [OWASP](https://owasp.org/) untuk security guidelines
- [CVE Database](https://cve.mitre.org/) untuk vulnerability references
- Open source security tools yang menginspirasi proyek ini

---

**⚠️ Disclaimer**: kodeXGuard adalah tool untuk ethical hacking dan security testing. Gunakan hanya pada sistem yang Anda miliki atau dengan izin eksplisit. Penggunaan untuk aktivitas ilegal adalah tanggung jawab pengguna.

'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { 
  ShieldCheckIcon, 
  BugAntIcon, 
  DocumentMagnifyingGlassIcon,
  ChatBubbleLeftRightIcon,
  PlayIcon
} from '@heroicons/react/24/outline';

const features = [
  {
    icon: ShieldCheckIcon,
    title: 'Vulnerability Scanner',
    description: 'Deteksi kerentanan SQL Injection, XSS, CSRF, dan 20+ jenis serangan lainn<PERSON>'
  },
  {
    icon: DocumentMagnifyingGlassIcon,
    title: 'File Analysis',
    description: 'Analisis file untuk mendeteksi malware, backdoor, dan kode berbahaya'
  },
  {
    icon: BugAntIcon,
    title: 'Penetration Testing',
    description: 'Automated penetration testing dengan AI untuk temukan celah keamanan'
  },
  {
    icon: ChatBubbleLeftRightIcon,
    title: 'Bot Integration',
    description: 'Integrasi WhatsApp & Telegram bot untuk monitoring real-time'
  },
];

const stats = [
  { value: '10,000+', label: 'Kerentanan Terdete<PERSON>' },
  { value: '500+', label: 'Pengguna Aktif' },
  { value: '99.9%', label: 'Akurasi Deteksi' },
  { value: '24/7', label: 'Monitoring' },
];

export function Hero() {
  const [currentFeature, setCurrentFeature] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentFeature((prev) => (prev + 1) % features.length);
    }, 3000);
    return () => clearInterval(interval);
  }, []);

  return (
    <section className="relative overflow-hidden bg-white dark:bg-secondary-900">
      {/* Background gradient */}
      <div className="absolute inset-0 bg-gradient-to-br from-primary-50 via-white to-secondary-50 dark:from-secondary-900 dark:via-secondary-900 dark:to-secondary-800" />
      
      {/* Grid pattern */}
      <div className="absolute inset-0 bg-[url('/grid.svg')] bg-center [mask-image:linear-gradient(180deg,white,rgba(255,255,255,0))] dark:[mask-image:linear-gradient(180deg,rgba(255,255,255,0.1),rgba(255,255,255,0))]" />

      <div className="relative mx-auto max-w-7xl px-6 py-24 sm:py-32 lg:px-8 lg:py-40">
        <div className="mx-auto max-w-4xl text-center">
          {/* Badge */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="mb-8"
          >
            <div className="inline-flex items-center rounded-full bg-primary-100 dark:bg-primary-900/20 px-4 py-2 text-sm font-medium text-primary-700 dark:text-primary-300 ring-1 ring-inset ring-primary-700/10 dark:ring-primary-300/20">
              🚀 Platform Keamanan Siber Terdepan di Indonesia
            </div>
          </motion.div>

          {/* Main heading */}
          <motion.h1
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="text-4xl font-bold tracking-tight text-secondary-900 dark:text-white sm:text-6xl lg:text-7xl"
          >
            Lindungi Aplikasi Anda dengan{' '}
            <span className="text-gradient-primary">
              kodeXGuard
            </span>
          </motion.h1>

          {/* Description */}
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="mt-6 text-lg leading-8 text-secondary-600 dark:text-secondary-300 max-w-3xl mx-auto"
          >
            Platform keamanan siber profesional yang menggunakan AI untuk mendeteksi kerentanan, 
            memindai file berbahaya, dan menjalankan penetration testing secara real-time. 
            Dapatkan laporan lengkap dan monitoring 24/7 untuk melindungi sistem Anda.
          </motion.p>

          {/* CTA buttons */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
            className="mt-10 flex flex-col sm:flex-row items-center justify-center gap-4"
          >
            <Link href="/auth/register">
              <Button size="lg" className="w-full sm:w-auto">
                Mulai Gratis Sekarang
              </Button>
            </Link>
            <Button variant="outline" size="lg" className="w-full sm:w-auto group">
              <PlayIcon className="mr-2 h-5 w-5 group-hover:scale-110 transition-transform" />
              Lihat Demo
            </Button>
          </motion.div>

          {/* Stats */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
            className="mt-16 grid grid-cols-2 gap-8 sm:grid-cols-4"
          >
            {stats.map((stat, index) => (
              <div key={stat.label} className="text-center">
                <div className="text-2xl sm:text-3xl font-bold text-secondary-900 dark:text-white">
                  {stat.value}
                </div>
                <div className="text-sm text-secondary-600 dark:text-secondary-400 mt-1">
                  {stat.label}
                </div>
              </div>
            ))}
          </motion.div>
        </div>

        {/* Features showcase */}
        <motion.div
          initial={{ opacity: 0, y: 40 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.7, delay: 0.5 }}
          className="mt-20 lg:mt-32"
        >
          <div className="mx-auto max-w-6xl">
            {/* Feature tabs */}
            <div className="flex flex-wrap justify-center gap-4 mb-12">
              {features.map((feature, index) => (
                <button
                  key={feature.title}
                  onClick={() => setCurrentFeature(index)}
                  className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-all duration-200 ${
                    currentFeature === index
                      ? 'bg-primary-100 dark:bg-primary-900/20 text-primary-700 dark:text-primary-300 ring-1 ring-primary-700/10 dark:ring-primary-300/20'
                      : 'text-secondary-600 dark:text-secondary-400 hover:text-secondary-900 dark:hover:text-secondary-100'
                  }`}
                >
                  <feature.icon className="h-5 w-5" />
                  <span className="text-sm font-medium">{feature.title}</span>
                </button>
              ))}
            </div>

            {/* Feature content */}
            <div className="text-center">
              <motion.div
                key={currentFeature}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
                className="space-y-4"
              >
                <div className="flex justify-center">
                  <div className="p-4 bg-primary-100 dark:bg-primary-900/20 rounded-2xl">
                    {features[currentFeature].icon && (
                      <features[currentFeature].icon className="h-12 w-12 text-primary-600 dark:text-primary-400" />
                    )}
                  </div>
                </div>
                <h3 className="text-2xl font-bold text-secondary-900 dark:text-white">
                  {features[currentFeature].title}
                </h3>
                <p className="text-lg text-secondary-600 dark:text-secondary-300 max-w-2xl mx-auto">
                  {features[currentFeature].description}
                </p>
              </motion.div>
            </div>

            {/* Mock dashboard preview */}
            <div className="mt-16 relative">
              <div className="relative mx-auto max-w-4xl">
                <div className="rounded-2xl bg-white dark:bg-secondary-800 shadow-2xl ring-1 ring-secondary-900/10 dark:ring-secondary-100/10 overflow-hidden">
                  <div className="bg-secondary-50 dark:bg-secondary-700 px-6 py-4 border-b border-secondary-200 dark:border-secondary-600">
                    <div className="flex items-center space-x-2">
                      <div className="w-3 h-3 bg-red-400 rounded-full"></div>
                      <div className="w-3 h-3 bg-yellow-400 rounded-full"></div>
                      <div className="w-3 h-3 bg-green-400 rounded-full"></div>
                      <div className="ml-4 text-sm text-secondary-600 dark:text-secondary-400">
                        kodeXGuard Dashboard
                      </div>
                    </div>
                  </div>
                  <div className="p-8">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      <div className="space-y-4">
                        <div className="h-4 bg-secondary-200 dark:bg-secondary-600 rounded animate-pulse"></div>
                        <div className="h-20 bg-secondary-100 dark:bg-secondary-700 rounded"></div>
                      </div>
                      <div className="space-y-4">
                        <div className="h-4 bg-secondary-200 dark:bg-secondary-600 rounded animate-pulse"></div>
                        <div className="h-20 bg-secondary-100 dark:bg-secondary-700 rounded"></div>
                      </div>
                      <div className="space-y-4">
                        <div className="h-4 bg-secondary-200 dark:bg-secondary-600 rounded animate-pulse"></div>
                        <div className="h-20 bg-secondary-100 dark:bg-secondary-700 rounded"></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}

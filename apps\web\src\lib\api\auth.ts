import { User, LoginSchema, RegisterSchema } from '@kodexguard/shared';
import { apiClient } from './client';

export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegisterRequest {
  email: string;
  username: string;
  fullName: string;
  password: string;
  confirmPassword: string;
}

export interface AuthResponse {
  user: User;
  accessToken: string;
  refreshToken: string;
}

export interface RefreshTokenResponse {
  accessToken: string;
  refreshToken?: string;
}

export const authApi = {
  async login(data: LoginRequest): Promise<AuthResponse> {
    // Validate input
    const validatedData = LoginSchema.parse(data);
    return apiClient.post('/api/auth/login', validatedData);
  },

  async register(data: RegisterRequest): Promise<AuthResponse> {
    // Validate input
    const validatedData = RegisterSchema.parse(data);
    return apiClient.post('/api/auth/register', validatedData);
  },

  async logout(): Promise<void> {
    return apiClient.post('/api/auth/logout');
  },

  async refreshToken(refreshToken: string): Promise<RefreshTokenResponse> {
    return apiClient.post('/api/auth/refresh', { refreshToken });
  },

  async getProfile(): Promise<User> {
    return apiClient.get('/api/auth/profile');
  },

  async updateProfile(data: Partial<User>): Promise<User> {
    return apiClient.put('/api/auth/profile', data);
  },

  async changePassword(data: {
    currentPassword: string;
    newPassword: string;
    confirmPassword: string;
  }): Promise<void> {
    return apiClient.post('/api/auth/change-password', data);
  },

  async forgotPassword(email: string): Promise<void> {
    return apiClient.post('/api/auth/forgot-password', { email });
  },

  async resetPassword(data: {
    token: string;
    password: string;
    confirmPassword: string;
  }): Promise<void> {
    return apiClient.post('/api/auth/reset-password', data);
  },

  async verifyEmail(token: string): Promise<void> {
    return apiClient.post('/api/auth/verify-email', { token });
  },

  async resendVerificationEmail(): Promise<void> {
    return apiClient.post('/api/auth/resend-verification');
  },
};

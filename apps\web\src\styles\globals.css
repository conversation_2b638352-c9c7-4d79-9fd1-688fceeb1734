@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@100;200;300;400;500;600;700;800&display=swap');

/* Base styles */
@layer base {
  html {
    scroll-behavior: smooth;
  }
  
  body {
    @apply bg-white dark:bg-secondary-900 text-secondary-900 dark:text-secondary-100;
    @apply font-sans antialiased;
  }
  
  /* Custom scrollbar */
  ::-webkit-scrollbar {
    @apply w-2;
  }
  
  ::-webkit-scrollbar-track {
    @apply bg-secondary-100 dark:bg-secondary-800;
  }
  
  ::-webkit-scrollbar-thumb {
    @apply bg-secondary-300 dark:bg-secondary-600 rounded-full;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    @apply bg-secondary-400 dark:bg-secondary-500;
  }
  
  /* Focus styles */
  *:focus {
    @apply outline-none ring-2 ring-primary-500 ring-offset-2 ring-offset-white dark:ring-offset-secondary-900;
  }
  
  /* Selection styles */
  ::selection {
    @apply bg-primary-100 text-primary-900;
  }
  
  ::-moz-selection {
    @apply bg-primary-100 text-primary-900;
  }
}

/* Component styles */
@layer components {
  /* Button variants */
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
  }
  
  .btn-primary {
    @apply bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500;
  }
  
  .btn-secondary {
    @apply bg-secondary-600 text-white hover:bg-secondary-700 focus:ring-secondary-500;
  }
  
  .btn-success {
    @apply bg-success-600 text-white hover:bg-success-700 focus:ring-success-500;
  }
  
  .btn-warning {
    @apply bg-warning-600 text-white hover:bg-warning-700 focus:ring-warning-500;
  }
  
  .btn-danger {
    @apply bg-danger-600 text-white hover:bg-danger-700 focus:ring-danger-500;
  }
  
  .btn-outline {
    @apply border border-secondary-300 dark:border-secondary-600 text-secondary-700 dark:text-secondary-300 hover:bg-secondary-50 dark:hover:bg-secondary-800;
  }
  
  .btn-ghost {
    @apply text-secondary-700 dark:text-secondary-300 hover:bg-secondary-100 dark:hover:bg-secondary-800;
  }
  
  /* Input styles */
  .input {
    @apply block w-full px-3 py-2 border border-secondary-300 dark:border-secondary-600 rounded-lg shadow-sm placeholder-secondary-400 dark:placeholder-secondary-500 bg-white dark:bg-secondary-800 text-secondary-900 dark:text-secondary-100 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500;
  }
  
  .input-error {
    @apply border-danger-300 dark:border-danger-600 focus:ring-danger-500 focus:border-danger-500;
  }
  
  /* Card styles */
  .card {
    @apply bg-white dark:bg-secondary-800 rounded-xl shadow-sm border border-secondary-200 dark:border-secondary-700;
  }
  
  .card-hover {
    @apply transition-all duration-200 hover:shadow-md hover:border-secondary-300 dark:hover:border-secondary-600;
  }
  
  /* Badge styles */
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }
  
  .badge-critical {
    @apply bg-critical-100 text-critical-800 dark:bg-critical-900 dark:text-critical-200;
  }
  
  .badge-high {
    @apply bg-high-100 text-high-800 dark:bg-high-900 dark:text-high-200;
  }
  
  .badge-medium {
    @apply bg-medium-100 text-medium-800 dark:bg-medium-900 dark:text-medium-200;
  }
  
  .badge-low {
    @apply bg-low-100 text-low-800 dark:bg-low-900 dark:text-low-200;
  }
  
  .badge-info {
    @apply bg-info-100 text-info-800 dark:bg-info-900 dark:text-info-200;
  }
  
  /* Loading spinner */
  .spinner {
    @apply animate-spin rounded-full border-2 border-secondary-200 border-t-primary-600;
  }
  
  /* Gradient backgrounds */
  .gradient-primary {
    @apply bg-gradient-to-r from-primary-600 to-primary-700;
  }
  
  .gradient-secondary {
    @apply bg-gradient-to-r from-secondary-600 to-secondary-700;
  }
  
  .gradient-success {
    @apply bg-gradient-to-r from-success-600 to-success-700;
  }
  
  .gradient-warning {
    @apply bg-gradient-to-r from-warning-600 to-warning-700;
  }
  
  .gradient-danger {
    @apply bg-gradient-to-r from-danger-600 to-danger-700;
  }
  
  /* Glass morphism */
  .glass {
    @apply backdrop-blur-md bg-white/10 dark:bg-secondary-900/10 border border-white/20 dark:border-secondary-700/20;
  }
  
  /* Text gradients */
  .text-gradient-primary {
    @apply bg-gradient-to-r from-primary-600 to-primary-700 bg-clip-text text-transparent;
  }
  
  .text-gradient-secondary {
    @apply bg-gradient-to-r from-secondary-600 to-secondary-700 bg-clip-text text-transparent;
  }
}

/* Utility styles */
@layer utilities {
  /* Hide scrollbar but keep functionality */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
  
  /* Custom animations */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }
  
  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }
  
  .animate-slide-down {
    animation: slideDown 0.3s ease-out;
  }
  
  /* Text utilities */
  .text-balance {
    text-wrap: balance;
  }
  
  /* Safe area utilities for mobile */
  .safe-top {
    padding-top: env(safe-area-inset-top);
  }
  
  .safe-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }
  
  .safe-left {
    padding-left: env(safe-area-inset-left);
  }
  
  .safe-right {
    padding-right: env(safe-area-inset-right);
  }
}

{"name": "@kodexguard/mobile", "version": "1.0.0", "description": "kodeXGuard Mobile App - React Native Application", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "start": "react-native start", "test": "jest", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "type-check": "tsc --noEmit", "clean": "react-native clean-project-auto"}, "dependencies": {"react": "18.2.0", "react-native": "0.72.6", "@react-navigation/native": "^6.1.9", "@react-navigation/stack": "^6.3.20", "@react-navigation/bottom-tabs": "^6.5.11", "@react-navigation/drawer": "^6.6.6", "react-native-screens": "^3.27.0", "react-native-safe-area-context": "^4.7.4", "react-native-gesture-handler": "^2.13.4", "react-native-reanimated": "^3.5.4", "react-native-sqlite-storage": "^6.0.1", "react-native-async-storage": "^1.19.5", "react-native-keychain": "^8.1.3", "react-native-vector-icons": "^10.0.2", "react-native-linear-gradient": "^2.8.3", "react-native-svg": "^13.14.0", "react-native-image-picker": "^7.0.3", "react-native-document-picker": "^9.1.1", "react-native-fs": "^2.20.0", "react-native-permissions": "^3.10.1", "react-native-device-info": "^10.11.0", "react-native-biometrics": "^3.0.1", "react-native-toast-message": "^2.1.6", "react-native-modal": "^13.0.1", "react-native-progress": "^5.0.1", "react-native-qrcode-scanner": "^1.5.5", "react-native-camera": "^4.2.1", "@tanstack/react-query": "^5.8.4", "zustand": "^4.4.7", "axios": "^1.6.2", "react-hook-form": "^7.48.2", "zod": "^3.22.4", "date-fns": "^2.30.0", "react-native-mmkv": "^2.10.2"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/eslint-config": "^0.72.0", "@react-native/metro-config": "^0.72.0", "@tsconfig/react-native": "^3.0.0", "@types/react": "^18.0.24", "@types/react-test-renderer": "^18.0.0", "@types/react-native-sqlite-storage": "^6.0.0", "@types/react-native-vector-icons": "^6.4.18", "babel-jest": "^29.2.1", "eslint": "^8.19.0", "jest": "^29.2.1", "metro-react-native-babel-preset": "0.76.8", "prettier": "^2.4.1", "react-test-renderer": "18.2.0", "typescript": "4.8.4", "react-native-clean-project": "^4.0.3"}, "engines": {"node": ">=16"}}
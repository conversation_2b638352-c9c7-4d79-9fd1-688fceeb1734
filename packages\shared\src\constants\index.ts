// ===========================
// Application Constants
// ===========================

export const APP_CONFIG = {
  NAME: 'kodeXGuard',
  VERSION: '1.0.0',
  DESCRIPTION: 'Platform Keamanan Siber dan Bug Hunting Profesional',
  AUTHOR: 'kodeXGuard Team',
  WEBSITE: 'https://kodexguard.com',
  SUPPORT_EMAIL: '<EMAIL>',
} as const;

// ===========================
// API Constants
// ===========================

export const API_ENDPOINTS = {
  AUTH: {
    LOGIN: '/api/auth/login',
    REGISTER: '/api/auth/register',
    REFRESH: '/api/auth/refresh',
    LOGOUT: '/api/auth/logout',
    VERIFY_EMAIL: '/api/auth/verify-email',
    FORGOT_PASSWORD: '/api/auth/forgot-password',
    RESET_PASSWORD: '/api/auth/reset-password',
  },
  SCAN: {
    CREATE: '/api/scan',
    LIST: '/api/scan',
    DETAIL: '/api/scan/:id',
    CANCEL: '/api/scan/:id/cancel',
    RETRY: '/api/scan/:id/retry',
  },
  TARGET: {
    CREATE: '/api/targets',
    LIST: '/api/targets',
    DETAIL: '/api/targets/:id',
    UPDATE: '/api/targets/:id',
    DELETE: '/api/targets/:id',
  },
  FILE: {
    UPLOAD: '/api/files/upload',
    ANALYZE: '/api/files/analyze',
    LIST: '/api/files',
    DETAIL: '/api/files/:id',
    DOWNLOAD: '/api/files/:id/download',
    DELETE: '/api/files/:id',
  },
  REPORT: {
    GENERATE: '/api/reports/generate',
    LIST: '/api/reports',
    DETAIL: '/api/reports/:id',
    DOWNLOAD: '/api/reports/:id/download',
    DELETE: '/api/reports/:id',
  },
  USER: {
    PROFILE: '/api/user/profile',
    UPDATE_PROFILE: '/api/user/profile',
    CHANGE_PASSWORD: '/api/user/change-password',
    SUBSCRIPTION: '/api/user/subscription',
  },
  ADMIN: {
    USERS: '/api/admin/users',
    STATS: '/api/admin/stats',
    SETTINGS: '/api/admin/settings',
  },
} as const;

// ===========================
// HTTP Status Codes
// ===========================

export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  CONFLICT: 409,
  UNPROCESSABLE_ENTITY: 422,
  TOO_MANY_REQUESTS: 429,
  INTERNAL_SERVER_ERROR: 500,
  SERVICE_UNAVAILABLE: 503,
} as const;

// ===========================
// Vulnerability Patterns
// ===========================

export const VULNERABILITY_PATTERNS = {
  SQL_INJECTION: [
    /error in your sql syntax/i,
    /mysql_fetch_array/i,
    /ora-\d{5}/i,
    /microsoft ole db provider/i,
    /unclosed quotation mark/i,
    /quoted string not properly terminated/i,
  ],
  XSS: [
    /<script[^>]*>.*?<\/script>/gi,
    /javascript:/gi,
    /on\w+\s*=/gi,
    /expression\s*\(/gi,
    /vbscript:/gi,
  ],
  LFI: [
    /\.\.\/.*\/etc\/passwd/i,
    /\.\.\/.*\/windows\/system32/i,
    /\.\.\/.*\/boot\.ini/i,
  ],
  COMMAND_INJECTION: [
    /;\s*(cat|ls|dir|type|echo|ping|wget|curl)/i,
    /\|\s*(cat|ls|dir|type|echo|ping|wget|curl)/i,
    /&&\s*(cat|ls|dir|type|echo|ping|wget|curl)/i,
  ],
} as const;

// ===========================
// File Analysis Constants
// ===========================

export const FILE_ANALYSIS = {
  MAX_FILE_SIZE: 50 * 1024 * 1024, // 50MB
  ALLOWED_EXTENSIONS: [
    '.php', '.js', '.zip', '.apk', '.exe', '.pdf', '.txt', '.json', '.xml'
  ],
  DANGEROUS_FUNCTIONS: {
    PHP: [
      'eval', 'exec', 'system', 'shell_exec', 'passthru', 'file_get_contents',
      'file_put_contents', 'fopen', 'fwrite', 'include', 'require', 'base64_decode'
    ],
    JAVASCRIPT: [
      'eval', 'Function', 'setTimeout', 'setInterval', 'document.write',
      'innerHTML', 'outerHTML', 'insertAdjacentHTML'
    ],
  },
  BACKDOOR_SIGNATURES: [
    'c99shell', 'r57shell', 'wso', 'b374k', 'adminer', 'phpshell',
    'webshell', 'backdoor', 'trojan', 'rat', 'keylogger'
  ],
} as const;

// ===========================
// Scan Configuration
// ===========================

export const SCAN_CONFIG = {
  DEFAULT_TIMEOUT: 5 * 60 * 1000, // 5 minutes
  MAX_CONCURRENT_SCANS: 5,
  MAX_REDIRECTS: 10,
  USER_AGENTS: [
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
  ],
  COMMON_PORTS: [
    21, 22, 23, 25, 53, 80, 110, 143, 443, 993, 995, 1433, 3306, 3389, 5432, 6379, 8080, 8443
  ],
  COMMON_PATHS: [
    '/admin', '/administrator', '/wp-admin', '/phpmyadmin', '/cpanel',
    '/login', '/signin', '/dashboard', '/panel', '/control', '/manage'
  ],
} as const;

// ===========================
// Bot Commands
// ===========================

export const BOT_COMMANDS = {
  WHATSAPP: {
    SCAN: '/scan',
    UPLOAD: '/upload',
    REPORT: '/report',
    STATUS: '/status',
    HELP: '/help',
    REGISTER: '/register',
    LOGIN: '/login',
    PROFILE: '/profile',
  },
  TELEGRAM: {
    START: '/start',
    SCAN: '/scan',
    UPLOAD: '/upload',
    REPORT: '/report',
    STATUS: '/status',
    HELP: '/help',
    REGISTER: '/register',
    LOGIN: '/login',
    PROFILE: '/profile',
    SETTINGS: '/settings',
  },
} as const;

// ===========================
// Notification Templates
// ===========================

export const NOTIFICATION_TEMPLATES = {
  SCAN_COMPLETED: {
    TITLE: 'Scan Completed',
    MESSAGE: 'Your scan for {target} has been completed. Found {vulnerabilityCount} vulnerabilities.',
  },
  SCAN_FAILED: {
    TITLE: 'Scan Failed',
    MESSAGE: 'Your scan for {target} has failed. Error: {error}',
  },
  FILE_ANALYZED: {
    TITLE: 'File Analysis Completed',
    MESSAGE: 'File {fileName} has been analyzed. Threat level: {threatLevel}',
  },
  SUBSCRIPTION_EXPIRED: {
    TITLE: 'Subscription Expired',
    MESSAGE: 'Your {plan} subscription has expired. Please renew to continue using premium features.',
  },
  QUOTA_EXCEEDED: {
    TITLE: 'Quota Exceeded',
    MESSAGE: 'You have exceeded your daily {quotaType} limit. Upgrade your plan for more quota.',
  },
} as const;

// ===========================
// Error Messages
// ===========================

export const ERROR_MESSAGES = {
  VALIDATION: {
    REQUIRED: 'Field ini wajib diisi',
    INVALID_EMAIL: 'Format email tidak valid',
    INVALID_URL: 'Format URL tidak valid',
    PASSWORD_TOO_SHORT: 'Password minimal 6 karakter',
    PASSWORD_MISMATCH: 'Password tidak cocok',
    INVALID_FILE_TYPE: 'Tipe file tidak didukung',
    FILE_TOO_LARGE: 'Ukuran file terlalu besar',
  },
  AUTH: {
    INVALID_CREDENTIALS: 'Email atau password salah',
    ACCOUNT_DISABLED: 'Akun Anda telah dinonaktifkan',
    EMAIL_NOT_VERIFIED: 'Email belum diverifikasi',
    TOKEN_EXPIRED: 'Token telah kedaluwarsa',
    UNAUTHORIZED: 'Anda tidak memiliki akses',
    FORBIDDEN: 'Akses ditolak',
  },
  SCAN: {
    TARGET_NOT_FOUND: 'Target tidak ditemukan',
    SCAN_IN_PROGRESS: 'Scan sedang berjalan',
    QUOTA_EXCEEDED: 'Kuota scan harian telah habis',
    INVALID_TARGET: 'Target tidak valid',
    SCAN_FAILED: 'Scan gagal dilakukan',
  },
  FILE: {
    UPLOAD_FAILED: 'Upload file gagal',
    FILE_NOT_FOUND: 'File tidak ditemukan',
    ANALYSIS_FAILED: 'Analisis file gagal',
    MALICIOUS_FILE: 'File terdeteksi berbahaya',
  },
  GENERAL: {
    INTERNAL_ERROR: 'Terjadi kesalahan internal',
    SERVICE_UNAVAILABLE: 'Layanan tidak tersedia',
    RATE_LIMIT_EXCEEDED: 'Terlalu banyak permintaan',
    NETWORK_ERROR: 'Kesalahan jaringan',
  },
} as const;

// ===========================
// Success Messages
// ===========================

export const SUCCESS_MESSAGES = {
  AUTH: {
    LOGIN_SUCCESS: 'Login berhasil',
    REGISTER_SUCCESS: 'Registrasi berhasil',
    LOGOUT_SUCCESS: 'Logout berhasil',
    PASSWORD_CHANGED: 'Password berhasil diubah',
    EMAIL_VERIFIED: 'Email berhasil diverifikasi',
  },
  SCAN: {
    SCAN_STARTED: 'Scan telah dimulai',
    SCAN_COMPLETED: 'Scan berhasil diselesaikan',
    TARGET_CREATED: 'Target berhasil ditambahkan',
    TARGET_UPDATED: 'Target berhasil diperbarui',
    TARGET_DELETED: 'Target berhasil dihapus',
  },
  FILE: {
    UPLOAD_SUCCESS: 'File berhasil diupload',
    ANALYSIS_COMPLETED: 'Analisis file selesai',
    FILE_CLEAN: 'File aman',
    FILE_DELETED: 'File berhasil dihapus',
  },
  REPORT: {
    GENERATED: 'Laporan berhasil dibuat',
    DOWNLOADED: 'Laporan berhasil diunduh',
    DELETED: 'Laporan berhasil dihapus',
  },
} as const;

// ===========================
// Regular Expressions
// ===========================

export const REGEX_PATTERNS = {
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  URL: /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/,
  DOMAIN: /^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}$/,
  IP_ADDRESS: /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,
  PASSWORD: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{6,}$/,
  PHONE: /^(\+62|62|0)8[1-9][0-9]{6,9}$/,
  USERNAME: /^[a-zA-Z0-9_]{3,20}$/,
} as const;

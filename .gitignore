# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage
.grunt

# Bower dependency directory
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons
build/Release

# Dependency directories
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Next.js
.next/
out/

# Nuxt.js
.nuxt
dist

# Gatsby files
.cache/
public

# Vuepress build output
.vuepress/dist

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# yarn v2
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# IDEs and editors
.idea
.vscode
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# kodeXGuard specific
# Database files
*.db
*.sqlite
*.sqlite3

# Upload directories
uploads/
quarantine/
temp/

# Log files
logs/
*.log

# Session files
sessions/

# Generated reports
reports/
*.pdf

# Scanner cache
scanner-cache/

# Bot session data
whatsapp-sessions/
telegram-sessions/

# AI model cache
ai-models/
model-cache/

# Security keys (never commit these!)
*.pem
*.key
*.crt
*.p12
*.pfx

# Backup files
*.bak
*.backup
*.old

# Test files
test-uploads/
test-results/

# Build artifacts
build/
dist/
.turbo/

# Mobile specific
# React Native
.expo/
.expo-shared/

# Android
android/app/build/
android/app/release/
android/.gradle/
android/local.properties

# iOS
ios/build/
ios/Pods/
ios/*.xcworkspace/xcuserdata/
ios/*.xcodeproj/xcuserdata/

# Flipper
ios/Flipper/

# Bundle artifacts
*.jsbundle

# CocoaPods
ios/Pods/

# Fastlane
fastlane/report.xml
fastlane/Preview.html
fastlane/screenshots
fastlane/test_output

# Metro
.metro-health-check*

{"name": "@kodexguard/shared", "version": "1.0.0", "description": "Shared utilities, types, and constants for kodeXGuard platform", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "rm -rf dist", "type-check": "tsc --noEmit", "lint": "eslint src --ext .ts,.tsx", "test": "jest"}, "dependencies": {"zod": "^3.22.4", "date-fns": "^2.30.0", "crypto-js": "^4.2.0", "uuid": "^9.0.1"}, "devDependencies": {"@types/crypto-js": "^4.2.1", "@types/uuid": "^9.0.7", "@types/jest": "^29.5.8", "jest": "^29.7.0", "ts-jest": "^29.1.1", "typescript": "^5.3.0"}, "peerDependencies": {"typescript": "^5.0.0"}, "files": ["dist", "src"], "publishConfig": {"access": "restricted"}}
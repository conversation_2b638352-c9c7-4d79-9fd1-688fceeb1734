{"name": "@kodexguard/web", "version": "1.0.0", "description": "kodeXGuard Web Frontend - Next.js Application", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "clean": "rm -rf .next out dist", "analyze": "cross-env ANALYZE=true next build"}, "dependencies": {"next": "^14.0.4", "react": "^18.2.0", "react-dom": "^18.2.0", "@kodexguard/shared": "workspace:*", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "clsx": "^2.0.0", "tailwind-merge": "^2.2.0", "framer-motion": "^10.16.16", "zustand": "^4.4.7", "@tanstack/react-query": "^5.14.2", "axios": "^1.6.2", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "zod": "^3.22.4", "react-hot-toast": "^2.4.1", "lucide-react": "^0.294.0", "recharts": "^2.8.0", "date-fns": "^2.30.0", "react-dropzone": "^14.2.3", "js-cookie": "^3.0.5", "next-themes": "^0.2.1"}, "devDependencies": {"@types/node": "^20.10.0", "@types/react": "^18.2.42", "@types/react-dom": "^18.2.17", "@types/js-cookie": "^3.0.6", "typescript": "^5.3.0", "eslint": "^8.54.0", "eslint-config-next": "^14.0.4", "@next/bundle-analyzer": "^14.0.4", "cross-env": "^7.0.3"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}
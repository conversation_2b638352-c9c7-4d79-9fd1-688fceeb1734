{"extends": "../../tsconfig.json", "compilerOptions": {"plugins": [{"name": "next"}], "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@/components/*": ["./src/components/*"], "@/lib/*": ["./src/lib/*"], "@/hooks/*": ["./src/hooks/*"], "@/store/*": ["./src/store/*"], "@/types/*": ["./src/types/*"], "@/utils/*": ["./src/utils/*"], "@/styles/*": ["./src/styles/*"], "@kodexguard/shared": ["../../packages/shared/src"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules", ".next", "out", "dist"]}
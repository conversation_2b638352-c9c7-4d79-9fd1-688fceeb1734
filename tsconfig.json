{"compilerOptions": {"target": "ES2022", "lib": ["ES2022", "DOM", "DOM.Iterable"], "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "module": "ESNext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "baseUrl": ".", "paths": {"@kodexguard/shared/*": ["./packages/shared/src/*"], "@kodexguard/api/*": ["./packages/api/src/*"], "@kodexguard/scanner/*": ["./packages/scanner/src/*"], "@kodexguard/file-analyzer/*": ["./packages/file-analyzer/src/*"], "@kodexguard/pdf-report/*": ["./packages/pdf-report/src/*"], "@kodexguard/bot/*": ["./packages/bot/src/*"], "@kodexguard/ai-assistant/*": ["./packages/ai-assistant/src/*"]}}, "include": ["**/*.ts", "**/*.tsx", "**/*.js", "**/*.jsx", ".next/types/**/*.ts"], "exclude": ["node_modules", ".next", "dist", "build", "coverage"]}
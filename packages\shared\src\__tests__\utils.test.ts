import {
  generateId,
  slugify,
  truncate,
  capitalizeFirst,
  sanitizeInput,
  isValidUrl,
  isValidEmail,
  isValidDomain,
  isValidIPAddress,
  hashPassword,
  verifyPassword,
  generateMD5,
  generateSHA256,
  formatDate,
  formatFileSize,
  getFileExtension,
  getMimeType,
  chunk,
  unique,
  randomInt,
  clamp,
  percentage,
  omit,
  pick,
  generateSecureToken,
  maskSensitiveData,
} from '../utils';

describe('String Utilities', () => {
  test('generateId should return a valid UUID', () => {
    const id = generateId();
    expect(id).toMatch(/^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i);
  });

  test('slugify should convert text to URL-friendly slug', () => {
    expect(slugify('Hello World!')).toBe('hello-world');
    expect(slugify('Test@#$%^&*()_+=')).toBe('test_');
    expect(slugify('  Multiple   Spaces  ')).toBe('multiple-spaces');
  });

  test('truncate should limit text length', () => {
    const longText = 'This is a very long text that should be truncated';
    expect(truncate(longText, 20)).toBe('This is a very long...');
    expect(truncate('Short', 20)).toBe('Short');
  });

  test('capitalizeFirst should capitalize first letter', () => {
    expect(capitalizeFirst('hello')).toBe('Hello');
    expect(capitalizeFirst('WORLD')).toBe('World');
    expect(capitalizeFirst('')).toBe('');
  });

  test('sanitizeInput should remove dangerous characters', () => {
    expect(sanitizeInput('<script>alert("xss")</script>')).toBe('scriptalert("xss")/script');
    expect(sanitizeInput('javascript:alert(1)')).toBe('alert(1)');
    expect(sanitizeInput('onclick=alert(1)')).toBe('alert(1)');
  });
});

describe('Validation Utilities', () => {
  test('isValidUrl should validate URLs correctly', () => {
    expect(isValidUrl('https://example.com')).toBe(true);
    expect(isValidUrl('http://localhost:3000')).toBe(true);
    expect(isValidUrl('not-a-url')).toBe(false);
    expect(isValidUrl('')).toBe(false);
  });

  test('isValidEmail should validate emails correctly', () => {
    expect(isValidEmail('<EMAIL>')).toBe(true);
    expect(isValidEmail('<EMAIL>')).toBe(true);
    expect(isValidEmail('invalid-email')).toBe(false);
    expect(isValidEmail('@domain.com')).toBe(false);
  });

  test('isValidDomain should validate domains correctly', () => {
    expect(isValidDomain('example.com')).toBe(true);
    expect(isValidDomain('sub.domain.org')).toBe(true);
    expect(isValidDomain('invalid')).toBe(false);
    expect(isValidDomain('.com')).toBe(false);
  });

  test('isValidIPAddress should validate IP addresses correctly', () => {
    expect(isValidIPAddress('***********')).toBe(true);
    expect(isValidIPAddress('127.0.0.1')).toBe(true);
    expect(isValidIPAddress('256.1.1.1')).toBe(false);
    expect(isValidIPAddress('192.168.1')).toBe(false);
  });
});

describe('Cryptography Utilities', () => {
  test('hashPassword and verifyPassword should work correctly', () => {
    const password = 'testpassword123';
    const hashed = hashPassword(password);
    
    expect(hashed).toContain(':');
    expect(verifyPassword(password, hashed)).toBe(true);
    expect(verifyPassword('wrongpassword', hashed)).toBe(false);
  });

  test('generateMD5 should generate correct MD5 hash', () => {
    const text = 'hello world';
    const hash = generateMD5(text);
    expect(hash).toBe('5d41402abc4b2a76b9719d911017c592');
  });

  test('generateSHA256 should generate correct SHA256 hash', () => {
    const text = 'hello world';
    const hash = generateSHA256(text);
    expect(hash).toBe('b94d27b9934d3e08a52e52d7da7dabfac484efe37a5380ee9088f7ace2efcde9');
  });
});

describe('File Utilities', () => {
  test('formatFileSize should format bytes correctly', () => {
    expect(formatFileSize(0)).toBe('0 Bytes');
    expect(formatFileSize(1024)).toBe('1 KB');
    expect(formatFileSize(1048576)).toBe('1 MB');
    expect(formatFileSize(1073741824)).toBe('1 GB');
  });

  test('getFileExtension should extract file extension', () => {
    expect(getFileExtension('test.txt')).toBe('txt');
    expect(getFileExtension('archive.tar.gz')).toBe('gz');
    expect(getFileExtension('noextension')).toBe('');
  });

  test('getMimeType should return correct MIME type', () => {
    expect(getMimeType('test.php')).toBe('application/x-php');
    expect(getMimeType('script.js')).toBe('application/javascript');
    expect(getMimeType('unknown.xyz')).toBe('application/octet-stream');
  });
});

describe('Array Utilities', () => {
  test('chunk should split array into chunks', () => {
    const array = [1, 2, 3, 4, 5, 6, 7, 8, 9];
    const chunks = chunk(array, 3);
    expect(chunks).toEqual([[1, 2, 3], [4, 5, 6], [7, 8, 9]]);
  });

  test('unique should remove duplicates', () => {
    const array = [1, 2, 2, 3, 3, 3, 4];
    expect(unique(array)).toEqual([1, 2, 3, 4]);
  });
});

describe('Number Utilities', () => {
  test('randomInt should generate number in range', () => {
    const num = randomInt(1, 10);
    expect(num).toBeGreaterThanOrEqual(1);
    expect(num).toBeLessThanOrEqual(10);
  });

  test('clamp should constrain value to range', () => {
    expect(clamp(5, 1, 10)).toBe(5);
    expect(clamp(-5, 1, 10)).toBe(1);
    expect(clamp(15, 1, 10)).toBe(10);
  });

  test('percentage should calculate percentage correctly', () => {
    expect(percentage(25, 100)).toBe(25);
    expect(percentage(1, 3)).toBe(33);
    expect(percentage(5, 0)).toBe(0);
  });
});

describe('Object Utilities', () => {
  test('omit should remove specified keys', () => {
    const obj = { a: 1, b: 2, c: 3 };
    expect(omit(obj, ['b'])).toEqual({ a: 1, c: 3 });
  });

  test('pick should select specified keys', () => {
    const obj = { a: 1, b: 2, c: 3 };
    expect(pick(obj, ['a', 'c'])).toEqual({ a: 1, c: 3 });
  });
});

describe('Security Utilities', () => {
  test('generateSecureToken should generate token of specified length', () => {
    const token = generateSecureToken(16);
    expect(token).toHaveLength(16);
    expect(token).toMatch(/^[A-Za-z0-9]+$/);
  });

  test('maskSensitiveData should mask data correctly', () => {
    expect(maskSensitiveData('1234567890', 2)).toBe('12******90');
    expect(maskSensitiveData('short', 2)).toBe('*****');
  });
});

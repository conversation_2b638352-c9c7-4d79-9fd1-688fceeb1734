import { z } from 'zod';

// ===========================
// User & Authentication Types
// ===========================

export enum UserRole {
  SUPER_ADMIN = 'super_admin',
  STAFF = 'staff',
  USER = 'user',
}

export enum SubscriptionPlan {
  FREE = 'free',
  PRO = 'pro',
  ENTERPRISE = 'enterprise',
}

export const UserSchema = z.object({
  id: z.string().uuid(),
  email: z.string().email(),
  username: z.string().min(3).max(50),
  fullName: z.string().min(2).max(100),
  role: z.nativeEnum(UserRole),
  subscriptionPlan: z.nativeEnum(SubscriptionPlan),
  isActive: z.boolean(),
  emailVerified: z.boolean(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export type User = z.infer<typeof UserSchema>;

export const LoginSchema = z.object({
  email: z.string().email(),
  password: z.string().min(6),
});

export const RegisterSchema = z.object({
  email: z.string().email(),
  username: z.string().min(3).max(50),
  fullName: z.string().min(2).max(100),
  password: z.string().min(6),
  confirmPassword: z.string().min(6),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

// ===========================
// Vulnerability Types
// ===========================

export enum VulnerabilityType {
  SQL_INJECTION = 'sql_injection',
  XSS = 'xss',
  CSRF = 'csrf',
  LFI = 'lfi',
  RFI = 'rfi',
  COMMAND_INJECTION = 'command_injection',
  XXE = 'xxe',
  SSRF = 'ssrf',
  DIRECTORY_TRAVERSAL = 'directory_traversal',
  AUTHENTICATION_BYPASS = 'authentication_bypass',
  SESSION_MANAGEMENT = 'session_management',
  INSECURE_HEADERS = 'insecure_headers',
  OPEN_REDIRECT = 'open_redirect',
  INFORMATION_DISCLOSURE = 'information_disclosure',
  BROKEN_ACCESS_CONTROL = 'broken_access_control',
}

export enum SeverityLevel {
  CRITICAL = 'critical',
  HIGH = 'high',
  MEDIUM = 'medium',
  LOW = 'low',
  INFO = 'info',
}

export const VulnerabilitySchema = z.object({
  id: z.string().uuid(),
  type: z.nativeEnum(VulnerabilityType),
  severity: z.nativeEnum(SeverityLevel),
  title: z.string(),
  description: z.string(),
  url: z.string().url().optional(),
  parameter: z.string().optional(),
  payload: z.string().optional(),
  evidence: z.string().optional(),
  recommendation: z.string(),
  cveId: z.string().optional(),
  cvssScore: z.number().min(0).max(10).optional(),
  discoveredAt: z.date(),
});

export type Vulnerability = z.infer<typeof VulnerabilitySchema>;

// ===========================
// Scan Types
// ===========================

export enum ScanType {
  WEBSITE = 'website',
  FILE = 'file',
  NETWORK = 'network',
  API = 'api',
}

export enum ScanStatus {
  PENDING = 'pending',
  RUNNING = 'running',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
}

export const ScanTargetSchema = z.object({
  id: z.string().uuid(),
  name: z.string().min(1).max(255),
  url: z.string().url(),
  description: z.string().optional(),
  userId: z.string().uuid(),
  isActive: z.boolean(),
  lastScanAt: z.date().optional(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export type ScanTarget = z.infer<typeof ScanTargetSchema>;

export const ScanResultSchema = z.object({
  id: z.string().uuid(),
  targetId: z.string().uuid(),
  type: z.nativeEnum(ScanType),
  status: z.nativeEnum(ScanStatus),
  vulnerabilities: z.array(VulnerabilitySchema),
  startedAt: z.date(),
  completedAt: z.date().optional(),
  duration: z.number().optional(), // in milliseconds
  totalVulnerabilities: z.number(),
  criticalCount: z.number(),
  highCount: z.number(),
  mediumCount: z.number(),
  lowCount: z.number(),
  infoCount: z.number(),
});

export type ScanResult = z.infer<typeof ScanResultSchema>;

// ===========================
// File Analysis Types
// ===========================

export enum FileType {
  PHP = 'php',
  JAVASCRIPT = 'javascript',
  ZIP = 'zip',
  APK = 'apk',
  EXE = 'exe',
  PDF = 'pdf',
  UNKNOWN = 'unknown',
}

export enum ThreatType {
  BACKDOOR = 'backdoor',
  TROJAN = 'trojan',
  RAT = 'rat',
  DDOS_TOOL = 'ddos_tool',
  MALWARE = 'malware',
  SUSPICIOUS_SCRIPT = 'suspicious_script',
  CLEAN = 'clean',
}

export const FileAnalysisSchema = z.object({
  id: z.string().uuid(),
  fileName: z.string(),
  fileSize: z.number(),
  fileType: z.nativeEnum(FileType),
  md5Hash: z.string(),
  sha256Hash: z.string(),
  threatType: z.nativeEnum(ThreatType),
  severity: z.nativeEnum(SeverityLevel),
  isQuarantined: z.boolean(),
  detectionDetails: z.string().optional(),
  scanEngine: z.string(),
  uploadedBy: z.string().uuid(),
  analyzedAt: z.date(),
});

export type FileAnalysis = z.infer<typeof FileAnalysisSchema>;

// ===========================
// Report Types
// ===========================

export enum ReportFormat {
  PDF = 'pdf',
  JSON = 'json',
  CSV = 'csv',
  XML = 'xml',
}

export const ReportSchema = z.object({
  id: z.string().uuid(),
  title: z.string(),
  description: z.string().optional(),
  format: z.nativeEnum(ReportFormat),
  scanResultId: z.string().uuid(),
  generatedBy: z.string().uuid(),
  filePath: z.string().optional(),
  downloadUrl: z.string().url().optional(),
  isPublic: z.boolean(),
  generatedAt: z.date(),
  expiresAt: z.date().optional(),
});

export type Report = z.infer<typeof ReportSchema>;

// ===========================
// API Response Types
// ===========================

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  timestamp: string;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// ===========================
// Bot Types
// ===========================

export enum BotPlatform {
  WHATSAPP = 'whatsapp',
  TELEGRAM = 'telegram',
}

export enum BotCommandType {
  SCAN = 'scan',
  UPLOAD = 'upload',
  REPORT = 'report',
  STATUS = 'status',
  HELP = 'help',
}

export const BotUserSchema = z.object({
  id: z.string().uuid(),
  platform: z.nativeEnum(BotPlatform),
  platformUserId: z.string(),
  username: z.string().optional(),
  fullName: z.string().optional(),
  userId: z.string().uuid().optional(), // linked user account
  isActive: z.boolean(),
  registeredAt: z.date(),
});

export type BotUser = z.infer<typeof BotUserSchema>;

// ===========================
// Subscription Types
// ===========================

export const SubscriptionSchema = z.object({
  id: z.string().uuid(),
  userId: z.string().uuid(),
  plan: z.nativeEnum(SubscriptionPlan),
  isActive: z.boolean(),
  startDate: z.date(),
  endDate: z.date().optional(),
  autoRenew: z.boolean(),
  paymentMethod: z.string().optional(),
  lastPaymentAt: z.date().optional(),
  nextBillingAt: z.date().optional(),
});

export type Subscription = z.infer<typeof SubscriptionSchema>;

// ===========================
// Feature Limits
// ===========================

export interface PlanLimits {
  maxScansPerDay: number;
  maxFileUploadsPerDay: number;
  maxTargets: number;
  maxReports: number;
  canUseBots: boolean;
  canUseAI: boolean;
  canExportReports: boolean;
  supportLevel: 'basic' | 'priority' | 'dedicated';
}

export const PLAN_LIMITS: Record<SubscriptionPlan, PlanLimits> = {
  [SubscriptionPlan.FREE]: {
    maxScansPerDay: 5,
    maxFileUploadsPerDay: 3,
    maxTargets: 2,
    maxReports: 5,
    canUseBots: false,
    canUseAI: false,
    canExportReports: false,
    supportLevel: 'basic',
  },
  [SubscriptionPlan.PRO]: {
    maxScansPerDay: 50,
    maxFileUploadsPerDay: 25,
    maxTargets: 20,
    maxReports: 100,
    canUseBots: true,
    canUseAI: true,
    canExportReports: true,
    supportLevel: 'priority',
  },
  [SubscriptionPlan.ENTERPRISE]: {
    maxScansPerDay: -1, // unlimited
    maxFileUploadsPerDay: -1, // unlimited
    maxTargets: -1, // unlimited
    maxReports: -1, // unlimited
    canUseBots: true,
    canUseAI: true,
    canExportReports: true,
    supportLevel: 'dedicated',
  },
};
